# 用户详情页面功能说明

## 功能概述

新增了用户详情页面功能，用户可以从用户信息列表页面点击"详情"按钮跳转到用户详情页面，查看用户的完整信息。

## 实现的功能

### 1. 页面跳转
- 在用户信息页面 (`/user-info`) 的每一行用户数据中，点击"详情"按钮
- 自动跳转到用户详情页面 (`/user/info-detail?id={userId}`)
- 通过URL参数传递用户ID

### 2. 用户详情页面
- **路径**: `/user/info-detail?id={userId}`
- **页面组件**: `src/pages/User/InfoDetail.tsx`
- **功能特性**:
  - 根据URL参数中的用户ID获取用户详情数据
  - 显示用户的完整信息，包括基本信息、内用信息、团队信息
  - 支持返回按钮，可以返回到用户列表页面
  - 支持复制用户ID和邮箱功能
  - 完整的国际化支持（中英文）

### 3. 页面布局

用户详情页面包含以下几个部分：

#### 基本信息卡片
- 用户头像和用户名
- 用户状态（正常/已禁用）
- 用户ID（可点击复制）
- 用户邮箱（可点击复制）
- 手机号
- 地区
- 注册来源
- 注册时间
- 认证状态

#### 内用信息卡片
- 用户身份（免费用户/付费用户等）
- 有效期
- 个人空间使用情况
- 个人空间总容量

#### 团队信息卡片
- 团队状态
- 团队有效期
- 团队制作容量ID
- 团队制作容量

## 技术实现

### 1. 路由配置
在 `src/App.tsx` 中添加了新的路由：
```typescript
<Route path="/user/info-detail" element={<UserInfoDetail />} />
```

### 2. 页面组件
- **组件位置**: `src/pages/User/InfoDetail.tsx`
- **使用的技术**:
  - React Hooks (useState, useEffect)
  - React Router (useSearchParams, useNavigate)
  - react-i18next 国际化
  - TypeScript 类型定义
  - Tailwind CSS 样式

### 3. 国际化支持
在 `src/locales/zh.json` 和 `src/locales/en.json` 中添加了完整的国际化文案：

#### 中文文案 (zh.json)
```json
"userInfoDetail": {
  "title": "用户详情",
  "description": "这是TailAdmin的React.js用户详情页面",
  "breadcrumb": "用户详情",
  "basicInfo": "用户基本信息",
  "storageInfo": "内用信息",
  "teamInfo": "团队信息",
  // ... 更多字段
}
```

#### 英文文案 (en.json)
```json
"userInfoDetail": {
  "title": "User Details",
  "description": "This is React.js User Details page for TailAdmin",
  "breadcrumb": "User Details",
  "basicInfo": "Basic Information",
  "storageInfo": "Storage Information",
  "teamInfo": "Team Information",
  // ... 更多字段
}
```

### 4. 数据模拟
实现了基于用户ID的模拟数据获取：
```typescript
const getMockUserDetailData = (userId: string): UserDetailData | null => {
  // 根据不同的用户ID返回对应的用户详情数据
}
```

## 使用方法

### 1. 从用户列表跳转
1. 访问用户信息页面 (`/user-info`)
2. 在用户列表中找到要查看的用户
3. 点击该用户行的"详情"按钮
4. 自动跳转到用户详情页面

### 2. 直接访问
也可以直接通过URL访问用户详情页面：
```
http://localhost:5174/user/info-detail?id=UID3232184
```

### 3. 支持的用户ID
目前模拟数据支持以下用户ID：
- `UID3232184` - 昱云
- `UID3232183` - 张三
- `6889e44b5353f300300e6cb6` - 昱云（原型中的ID）

## 样式设计

页面样式完全参考了原有的用户信息页面 (`src/pages/User/Info.tsx`)：
- 使用相同的组件库（ComponentCard, Label, Badge, Button等）
- 保持一致的布局和间距
- 响应式设计，支持移动端
- 深色模式支持

## 错误处理

- 如果URL中没有用户ID参数，会显示错误提示并自动跳转回用户列表
- 如果用户ID不存在，会显示"用户不存在"的错误信息
- 加载过程中显示加载状态

## 扩展性

该实现具有良好的扩展性：
- 可以轻松添加更多用户信息字段
- 支持真实API接口替换模拟数据
- 可以添加编辑用户信息的功能
- 可以添加更多操作按钮（如编辑、删除等）

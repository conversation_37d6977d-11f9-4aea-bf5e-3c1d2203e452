# Ant Design 集成指南

## 1. 安装 Ant Design

```bash
npm install antd
# 或
yarn add antd
```

## 2. 配置主题对齐

### 创建 Ant Design 主题配置

```typescript
// src/config/antd-theme.ts
import { ThemeConfig } from "antd";

export const antdTheme: ThemeConfig = {
  token: {
    // 使用项目的品牌色
    colorPrimary: "#465fff", // 对应 --color-brand-500
    colorSuccess: "#12b76a", // 对应 --color-success-500
    colorWarning: "#f79009", // 对应 --color-warning-500
    colorError: "#f04438", // 对应 --color-error-500
    colorInfo: "#0ba5ec", // 对应 --color-blue-light-500

    // 字体配置
    fontFamily: "Outfit, sans-serif",
    fontSize: 14,

    // 圆角配置
    borderRadius: 8,

    // 阴影配置
    boxShadow: "0px 1px 3px 0px rgba(16, 24, 40, 0.1), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)",
  },
  components: {
    Button: {
      borderRadius: 8,
      controlHeight: 44, // 对应 md size
      paddingContentHorizontal: 20,
    },
    Input: {
      borderRadius: 8,
      controlHeight: 44,
      paddingContentHorizontal: 16,
    },
    Select: {
      borderRadius: 8,
      controlHeight: 44,
    },
    Table: {
      borderRadius: 12,
      headerBg: "#f9fafb", // 对应 --color-gray-50
    },
  },
};
```

### 在应用中配置主题

```typescript
// src/App.tsx 或 main.tsx
import { ConfigProvider } from 'antd';
import { antdTheme } from './config/antd-theme';

function App() {
  return (
    <ConfigProvider theme={antdTheme}>
      {/* 你的应用内容 */}
    </ConfigProvider>
  );
}
```

## 3. 样式加载顺序

确保在 `src/index.css` 中正确的加载顺序：

```css
/* 1. 字体 */
@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@100..900&display=swap")
layer(base);

/* 2. Tailwind CSS */
@import "tailwindcss";

/* 3. Ant Design 样式 (如果需要全局导入) */
/* @import 'antd/dist/reset.css'; */

/* 4. 自定义样式 */
@custom-variant dark (&:is(.dark *));
/* ... 其他自定义样式 */
```

## 4. 混合使用策略

### 推荐的组件选择策略：

#### 使用 Ant Design 的场景：

- **复杂数据展示**: Table (分页、排序、筛选)
- **表单组件**: Form, DatePicker, TimePicker, Upload
- **数据输入**: AutoComplete, Cascader, TreeSelect
- **反馈组件**: Modal, Drawer, Notification, Message
- **导航组件**: Menu, Breadcrumb, Pagination, Steps

#### 继续使用现有组件的场景：

- **基础UI**: Button, Badge, Card
- **简单表单**: Input, Select (如果功能足够)
- **布局组件**: 继续使用 Tailwind 的 grid/flex

### 示例：混合使用

```typescript
// 使用 Ant Design 的复杂表格
import { Table as AntTable, Button as AntButton } from 'antd';
// 使用项目现有的简单组件
import Button from '@component/ui/button/Button';
import Badge from '@component/ui/badge/Badge';

function UserManagement() {
  return (
    <div className="space-y-6">
      {/* 使用现有的 Button 组件 */}
      <div className="flex gap-3">
        <Button variant="primary">新增用户</Button>
        <Button variant="outline">导出数据</Button>
      </div>

      {/* 使用 Ant Design 的复杂表格 */}
      <AntTable
        columns={columns}
        dataSource={data}
        pagination={{
          total: 100,
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        scroll={{ x: 800 }}
      />
    </div>
  );
}
```

## 5. 暗色模式适配

Ant Design 5.x 完全支持暗色模式，可以与你项目的暗色主题完美集成：

### 动态主题切换

```typescript
// src/config/antd-theme.ts
import { theme } from "antd";
import type { ThemeConfig } from "antd";

export const getAntdTheme = (isDark: boolean): ThemeConfig => ({
  // 使用 Ant Design 的暗色算法
  algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,

  token: {
    // 品牌色保持一致
    colorPrimary: "#465fff",
    colorSuccess: "#12b76a",
    colorWarning: "#f79009",
    colorError: "#f04438",
    colorInfo: "#0ba5ec",

    // 字体配置
    fontFamily: "Outfit, sans-serif",
    fontSize: 14,
    borderRadius: 8,

    // 根据主题动态调整背景色，与你的项目保持一致
    colorBgContainer: isDark ? "#1a2231" : "#ffffff", // 对应 gray-dark : white
    colorBgElevated: isDark ? "#262d3c" : "#ffffff", // 卡片背景
    colorBgLayout: isDark ? "#0c111d" : "#f9fafb", // 对应 gray-950 : gray-50

    // 文本颜色
    colorText: isDark ? "rgba(255, 255, 255, 0.9)" : "#101828", // 主文本
    colorTextSecondary: isDark ? "rgba(255, 255, 255, 0.65)" : "#667085", // 次要文本
    colorTextTertiary: isDark ? "rgba(255, 255, 255, 0.45)" : "#98a2b3", // 三级文本

    // 边框颜色
    colorBorder: isDark ? "rgba(255, 255, 255, 0.05)" : "#e4e7ec", // 对应项目的边框色
    colorBorderSecondary: isDark ? "rgba(255, 255, 255, 0.03)" : "#f2f4f7",
  },

  components: {
    Button: {
      borderRadius: 8,
      controlHeight: 44,
      paddingContentHorizontal: 20,
    },
    Input: {
      borderRadius: 8,
      controlHeight: 44,
      paddingContentHorizontal: 16,
    },
    Select: {
      borderRadius: 8,
      controlHeight: 44,
    },
    Table: {
      borderRadius: 12,
      // 表头背景色根据主题调整
      headerBg: isDark ? "rgba(255, 255, 255, 0.03)" : "#f9fafb",
      headerColor: isDark ? "rgba(255, 255, 255, 0.65)" : "#667085",
    },
    Card: {
      borderRadius: 12,
    },
    Modal: {
      borderRadius: 12,
    },
  },
});
```

### 与你现有的主题系统集成

由于你的项目已经有完整的主题系统，我们只需要简单修改即可：

```typescript
// src/App.tsx - 修改现有的 App 组件
import { BrowserRouter as Router, Routes, Route } from "react-router";
import { ConfigProvider } from 'antd'; // 新增
import { getAntdTheme } from './config/antd-theme'; // 新增
import { useTheme } from './context/ThemeContext'; // 已存在
// ... 其他导入

export default function App() {
  const { theme } = useTheme(); // 使用现有的主题上下文
  const isDark = theme === 'dark';

  return (
    <ConfigProvider theme={getAntdTheme(isDark)}>
      <Router>
        <ScrollToTop />
        <Routes>
          {/* 现有的路由配置保持不变 */}
          <Route element={<AppLayout />}>
            <Route index path="/" element={<Home />} />
            {/* ... 其他路由 */}
          </Route>
        </Routes>
      </Router>

      {/* 现有的 Toast 配置保持不变 */}
      <Toaster
        position="top-center"
        reverseOrder={false}
        gutter={8}
        containerClassName="toast-container"
        containerStyle={{ zIndex: 99999 }}
        toastOptions={toastOptions}
      />
    </ConfigProvider>
  );
}
```

### 在 main.tsx 中包装 ThemeProvider

```typescript
// src/main.tsx - 确保 ThemeProvider 在最外层
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { HelmetProvider } from 'react-helmet-async';
import { ThemeProvider } from './context/ThemeContext'; // 已存在
import App from './App';
import './index.css';

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <HelmetProvider>
      <ThemeProvider>
        <App />
      </ThemeProvider>
    </HelmetProvider>
  </StrictMode>
);
```

### 实际效果示例

```typescript
// src/components/examples/AntdDarkModeExample.tsx
import { Table, Button, Card, Form, Input, Select, DatePicker } from 'antd';
import { useState } from 'react';

const AntdDarkModeExample = () => {
  const [form] = Form.useForm();

  const columns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <span className={`px-2 py-1 rounded text-xs ${
          status === 'active'
            ? 'bg-success-50 text-success-700 dark:bg-success-500/20 dark:text-success-400'
            : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
        }`}>
          {status === 'active' ? '活跃' : '非活跃'}
        </span>
      ),
    },
  ];

  const data = [
    { key: '1', username: '张三', email: '<EMAIL>', status: 'active' },
    { key: '2', username: '李四', email: '<EMAIL>', status: 'inactive' },
  ];

  return (
    <div className="space-y-6">
      {/* Ant Design Card 在暗色模式下的表现 */}
      <Card title="用户管理" className="shadow-theme-sm">
        <Form form={form} layout="vertical" className="mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Form.Item label="用户名" name="username">
              <Input placeholder="请输入用户名" />
            </Form.Item>
            <Form.Item label="邮箱" name="email">
              <Input placeholder="请输入邮箱" />
            </Form.Item>
            <Form.Item label="注册日期" name="date">
              <DatePicker className="w-full" placeholder="选择日期" />
            </Form.Item>
          </div>
          <div className="flex gap-3">
            <Button type="primary">搜索</Button>
            <Button>重置</Button>
          </div>
        </Form>

        {/* Ant Design Table 在暗色模式下自动适配 */}
        <Table
          columns={columns}
          dataSource={data}
          pagination={{
            total: 50,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Card>
    </div>
  );
};

export default AntdDarkModeExample;
```

### 主题切换按钮示例

```typescript
// src/components/ThemeToggle.tsx
import { Button } from 'antd';
import { MoonOutlined, SunOutlined } from '@ant-design/icons';
import { useTheme } from '../hooks/useTheme';

const ThemeToggle = () => {
  const { isDark, toggleTheme } = useTheme();

  return (
    <Button
      type="text"
      icon={isDark ? <SunOutlined /> : <MoonOutlined />}
      onClick={toggleTheme}
      className="flex items-center justify-center"
    >
      {isDark ? '浅色模式' : '暗色模式'}
    </Button>
  );
};

export default ThemeToggle;
```

## 6. 按需引入优化

```typescript
// 只引入需要的组件
import { Table, Form, DatePicker, Modal } from "antd";

// 或者使用 babel-plugin-import 进行自动按需引入
```

## 7. 迁移建议

### 渐进式迁移：

1. **第一阶段**: 在新功能中使用 Ant Design 的复杂组件
2. **第二阶段**: 逐步替换现有的复杂组件（如表格、表单）
3. **第三阶段**: 评估是否需要统一所有组件

### 保持一致性：

- 使用统一的主题配置
- 保持相同的交互模式
- 统一的间距和布局规范

## 总结

Ant Design 可以与你的 Tailwind CSS 项目无缝集成，建议：

1. **优先使用 Ant Design 的复杂组件**（Table, Form, DatePicker 等）
2. **保留现有的简单组件**（Button, Badge 等）
3. **通过主题配置确保视觉一致性**
4. **采用渐进式迁移策略**

这样既能享受 Ant Design 的强大功能，又能保持项目的设计一致性。

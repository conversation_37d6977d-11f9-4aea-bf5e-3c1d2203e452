import { theme } from "antd";
import type { ThemeConfig } from "antd";

/**
 * 获取 Ant Design 主题配置
 * 与项目现有的 Tailwind CSS 设计系统保持一致
 */
export const getAntdTheme = (isDark: boolean): ThemeConfig => ({
  // 使用 Ant Design 的暗色算法
  algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,

  token: {
    // === 品牌色系 - 与 Tailwind 配置保持一致 ===
    colorPrimary: "#465fff", // --color-brand-500
    colorSuccess: "#12b76a", // --color-success-500
    colorWarning: "#f79009", // --color-warning-500
    colorError: "#f04438", // --color-error-500
    colorInfo: "#0ba5ec", // --color-blue-light-500

    // === 字体配置 ===
    fontFamily: "Outfit, sans-serif",
    fontSize: 14, // --text-theme-sm
    fontSizeLG: 16, // 大号文字
    fontSizeSM: 12, // --text-theme-xs

    // === 圆角配置 ===
    borderRadius: 8, // 与项目组件保持一致
    borderRadiusLG: 12, // 大圆角，用于卡片等
    borderRadiusSM: 6, // 小圆角

    // === 间距配置 ===
    padding: 16,
    paddingLG: 20,
    paddingSM: 12,
    paddingXS: 8,

    // === 动态背景色 - 根据主题调整 ===
    colorBgContainer: isDark ? "#1a2231" : "#ffffff", // gray-dark : white
    colorBgElevated: isDark ? "#262d3c" : "#ffffff", // 卡片/弹窗背景
    colorBgLayout: isDark ? "#0c111d" : "#f9fafb", // gray-950 : gray-50
    colorBgSpotlight: isDark ? "#344054" : "#f2f4f7", // 高亮背景

    // === 动态文本色 ===
    colorText: isDark ? "rgba(255, 255, 255, 0.9)" : "#101828", // 主文本
    colorTextSecondary: isDark ? "rgba(255, 255, 255, 0.65)" : "#667085", // 次要文本
    colorTextTertiary: isDark ? "rgba(255, 255, 255, 0.45)" : "#98a2b3", // 三级文本
    colorTextQuaternary: isDark ? "rgba(255, 255, 255, 0.25)" : "#d0d5dd", // 四级文本

    // === 动态边框色 ===
    colorBorder: isDark ? "rgba(255, 255, 255, 0.05)" : "#e4e7ec", // 主边框
    colorBorderSecondary: isDark ? "rgba(255, 255, 255, 0.03)" : "#f2f4f7", // 次要边框

    // === 阴影配置 - 与项目保持一致 ===
    boxShadow: "0px 1px 3px 0px rgba(16, 24, 40, 0.1), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)",
    boxShadowSecondary:
      "0px 4px 8px -2px rgba(16, 24, 40, 0.1), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)",

    // === 控件高度 ===
    controlHeight: 44, // 与项目 Button md 尺寸一致
    controlHeightLG: 48, // 大尺寸
    controlHeightSM: 36, // 小尺寸
  },

  components: {
    // === Button 组件 ===
    Button: {
      borderRadius: 8,
      controlHeight: 44, // md 尺寸
      controlHeightSM: 36, // sm 尺寸
      controlHeightLG: 48, // lg 尺寸
      paddingContentHorizontal: 20, // 与项目 Button 一致
      paddingContentHorizontalSM: 16,
      fontWeight: 500,
    },

    // === Input 组件 ===
    Input: {
      borderRadius: 8,
      controlHeight: 44,
      paddingContentHorizontal: 16,
      fontSize: 14,
    },

    // === Select 组件 ===
    Select: {
      borderRadius: 8,
      controlHeight: 44,
      fontSize: 14,
    },

    // === Table 组件 ===
    Table: {
      borderRadius: 12, // 与项目卡片圆角一致
      headerBg: isDark ? "rgba(255, 255, 255, 0.03)" : "#f9fafb",
      headerColor: isDark ? "rgba(255, 255, 255, 0.65)" : "#667085",
      fontSize: 14,
      cellPaddingBlock: 12, // 垂直内边距
      cellPaddingInline: 20, // 水平内边距
    },

    // === Card 组件 ===
    Card: {
      borderRadius: 12,
      paddingLG: 24,
      headerBg: "transparent",
    },

    // === Modal 组件 ===
    Modal: {
      borderRadius: 12,
      paddingContentHorizontal: 24,
      paddingMD: 24,
    },

    // === Form 组件 ===
    Form: {
      labelFontSize: 14,
      labelColor: isDark ? "rgba(255, 255, 255, 0.9)" : "#344054",
      itemMarginBottom: 20,
    },

    // === DatePicker 组件 ===
    DatePicker: {
      borderRadius: 8,
      controlHeight: 44,
    },

    // === Message 组件 ===
    Message: {
      borderRadius: 8,
      fontSize: 14,
    },

    // === Notification 组件 ===
    Notification: {
      borderRadius: 12,
      paddingMD: 16,
    },

    // === Menu 组件 ===
    Menu: {
      borderRadius: 8,
      itemBorderRadius: 6,
      itemHeight: 44, // 与项目菜单项高度一致
      fontSize: 14,
    },

    // === Dropdown 组件 ===
    Dropdown: {
      borderRadius: 8,
      paddingBlock: 8,
    },

    // === Tooltip 组件 ===
    Tooltip: {
      borderRadius: 6,
      fontSize: 12,
    },

    // === Badge 组件 ===
    Badge: {
      borderRadius: 4,
      fontSize: 12,
    },

    // === Pagination 组件 ===
    Pagination: {
      borderRadius: 6,
      itemSize: 36,
    },

    // === Tabs 组件 ===
    Tabs: {
      borderRadius: 6,
      fontSize: 14,
      fontWeightStrong: 500,
    },
  },
});

/**
 * 静态主题配置（如果不需要动态切换）
 */
export const antdLightTheme: ThemeConfig = getAntdTheme(false);
export const antdDarkTheme: ThemeConfig = getAntdTheme(true);

{"nav": {"dashboard": "仪表板", "ecommerce": "电商", "calendar": "日历", "userProfile": "用户资料", "users": "用户", "userInfo": "用户信息", "userProductionStats": "用户制作统计", "inviteCodeManagement": "邀请码管理", "forms": "表单", "formElements": "表单元素", "tables": "表格", "basicTables": "基础表格", "pages": "页面", "blankPage": "空白页", "404Error": "404错误", "charts": "图表", "lineChart": "折线图", "barChart": "柱状图", "uiElements": "UI元素", "alerts": "警告", "avatar": "头像", "badge": "徽章", "buttons": "按钮", "images": "图片", "videos": "视频", "resources": "资源", "sound": "配音列表", "digitalHuman": "数字人管理", "renderLog": "渲染日志", "authentication": "身份验证", "signIn": "登录", "signUp": "注册"}, "header": {"search": "搜索或输入命令...", "notifications": "通知", "profile": "个人资料", "settings": "设置", "logout": "退出登录"}, "auth": {"layout": {"description": "免费开源的Tailwind CSS管理仪表板模板"}, "signIn": {"title": "登录", "subtitle": "输入您的邮箱和密码进行登录！", "backToDashboard": "返回仪表板", "signInWithGoogle": "使用Google登录", "signInWithX": "使用X登录", "or": "或", "email": "邮箱", "password": "密码", "emailPlaceholder": "<EMAIL>", "passwordPlaceholder": "请输入您的密码", "keepLoggedIn": "保持登录状态", "forgotPassword": "忘记密码？", "signInButton": "登录", "noAccount": "还没有账户？", "signUpLink": "注册", "required": "*"}, "signUp": {"title": "注册", "subtitle": "输入您的邮箱和密码进行注册！", "backToDashboard": "返回仪表板", "signUpWithGoogle": "使用Google注册", "signUpWithX": "使用X注册", "or": "或", "firstName": "名字", "lastName": "姓氏", "email": "邮箱", "password": "密码", "firstNamePlaceholder": "请输入您的名字", "lastNamePlaceholder": "请输入您的姓氏", "emailPlaceholder": "请输入您的邮箱", "passwordPlaceholder": "请输入您的密码", "termsAgreement": "创建账户即表示您同意", "termsAndConditions": "条款和条件，", "and": "以及我们的", "privacyPolicy": "隐私政策", "signUpButton": "注册", "haveAccount": "已经有账户了？", "signInLink": "登录", "required": "*"}}, "pages": {"dashboard": {"title": "仪表板", "ecommerce": "电商仪表板", "description": "这是TailAdmin的React.js电商仪表板页面 - React.js Tailwind CSS管理仪表板模板"}, "profile": {"title": "个人资料", "description": "这是TailAdmin的React.js个人资料仪表板页面 - React.js Tailwind CSS管理仪表板模板", "breadcrumb": "个人资料"}, "calendar": {"title": "日历", "description": "这是TailAdmin的React.js日历页面 - React.js Tailwind CSS管理仪表板模板"}, "forms": {"title": "表单元素", "description": "这是TailAdmin的React.js表单元素页面 - React.js Tailwind CSS管理仪表板模板"}, "tables": {"title": "基础表格", "description": "这是TailAdmin的React.js基础表格页面 - React.js Tailwind CSS管理仪表板模板"}, "charts": {"lineChart": {"title": "折线图", "description": "这是TailAdmin的React.js折线图页面 - React.js Tailwind CSS管理仪表板模板"}, "barChart": {"title": "柱状图", "description": "这是TailAdmin的React.js柱状图页面 - React.js Tailwind CSS管理仪表板模板"}}, "uiElements": {"alerts": {"title": "警告", "description": "这是TailAdmin的React.js警告页面 - React.js Tailwind CSS管理仪表板模板"}, "avatars": {"title": "头像", "description": "这是TailAdmin的React.js头像页面 - React.js Tailwind CSS管理仪表板模板"}, "badges": {"title": "徽章", "description": "这是TailAdmin的React.js徽章页面 - React.js Tailwind CSS管理仪表板模板"}, "buttons": {"title": "按钮", "description": "这是TailAdmin的React.js按钮页面 - React.js Tailwind CSS管理仪表板模板"}, "images": {"title": "图片", "description": "这是TailAdmin的React.js图片页面 - React.js Tailwind CSS管理仪表板模板"}, "videos": {"title": "视频", "description": "这是TailAdmin的React.js视频页面 - React.js Tailwind CSS管理仪表板模板"}}, "notFound": {"title": "404 - 页面未找到", "heading": "哎呀！页面未找到", "description": "您要查找的页面可能已被删除、更名或暂时不可用。", "backHome": "返回首页"}, "blank": {"title": "空白页", "description": "这是TailAdmin的React.js空白页面 - React.js Tailwind CSS管理仪表板模板", "content": "这是一个空白页面。您可以在这里添加您的内容。"}, "userInfo": {"title": "用户信息", "description": "这是TailAdmin的React.js用户信息页面 - React.js Tailwind CSS管理仪表板模板", "breadcrumb": "用户信息", "conditionQuery": "条件查询", "conditionField": {"select": "请选择", "username": "用户名", "userId": "用户ID", "userEmail": "用户邮箱", "inviteCode": "邀请码"}, "conditionValue": {"placeholder": "请输入关键词"}, "registerTime": "注册时间", "region": "地区", "registerSource": "注册来源", "query": "查询", "collapse": "收起", "username": "用户名", "userId": "用户ID", "userEmail": "用户邮箱", "status": "状态", "actions": "操作", "details": "详情", "modify": "更改", "active": "正常", "copied": "已复制", "copyFailed": "复制失败", "regions": {"all": "全部", "china": "中国", "singapore": "新加坡", "usa": "美国", "uk": "英国", "canada": "加拿大", "hongkong": "中国香港"}, "sources": {"all": "全部", "inviteCode": "邀请码"}, "userActions": {"blockUser": "封禁用户", "deleteUser": "删除用户", "unblockUser": "解除封禁", "more": "更多"}}, "userInfoDetail": {"title": "用户详情", "description": "这是TailAdmin的React.js用户详情页面 - React.js Tailwind CSS管理仪表板模板", "breadcrumb": "用户详情", "basicInfo": "用户基本信息", "storageInfo": "内用信息", "teamInfo": "团队信息", "userId": "用户ID", "userEmail": "用户邮箱", "phone": "手机号", "region": "地区", "registerSource": "注册来源", "registerTime": "注册时间", "verificationStatus": "认证状态", "unverified": "未认证", "userType": "用户身份", "freeUser": "免费用户", "validity": "有效期", "personalSpaceUsed": "个人空间使用情况", "personalSpaceLimit": "个人空间总容量", "teamStatus": "团队状态", "teamExpiry": "有效期", "teamId": "团队制作容量ID", "teamCapacity": "团队制作容量", "status": {"active": "正常", "inactive": "已禁用"}, "copied": "已复制", "copyFailed": "复制失败", "invalidUserId": "无效的用户ID", "userNotFound": "用户不存在"}, "userProductionStats": {"title": "用户制作统计", "description": "这是TailAdmin的React.js用户制作统计页面 - React.js Tailwind CSS管理仪表板模板", "breadcrumb": "用户制作统计", "queryConditions": "查询条件", "conditionQuery": "条件查询", "conditionField": {"select": "请选择", "username": "用户名", "userId": "用户ID", "userEmail": "用户邮箱"}, "conditionValue": {"placeholder": "请输入关键词"}, "query": "查询", "collapse": "收起", "tabs": {"all": "全部", "smartAdult": "智能成人", "pictureExplain": "图文解说", "linkShopExplain": "连播解说", "smartPhotoExplain": "智能图片解说", "digitalAvatar": "口播数字人"}, "username": "用户名", "userId": "用户ID", "userEmail": "用户邮箱", "yesterdayProduction": "昨日制作量", "totalProduction": "累计制作量", "actions": "操作", "details": "详情", "copied": "已复制", "copyFailed": "复制失败", "queryComplete": "查询完成！", "formReset": "表单已重置", "navigatingToDetails": "正在跳转到用户详情..."}, "sound": {"title": "配音列表", "description": "这是TailAdmin的React.js配音列表页面 - React.js Tailwind CSS管理仪表板模板", "breadcrumb": "配音列表", "conditionQuery": "条件查询", "conditionField": {"select": "请选择", "voiceId": "配音ID", "voiceName": "名称", "userEmail": "邮箱"}, "conditionValue": {"placeholder": "请输入关键词"}, "createDate": "创建日期", "serviceProvider": "服务商", "sortBy": "排序方式", "sortOptions": {"mostPopular": "最受欢迎", "newest": "最新上线", "aToZ": "A-Z", "zToA": "Z-A"}, "query": "查询", "collapse": "收起", "voiceName": "名称", "preview": "试听", "usageCount": "使用数", "favoriteCount": "收藏次数", "actions": "操作", "play": "播放", "edit": "编辑", "copyId": "复制ID", "copied": "已复制", "copyFailed": "复制失败", "serviceProviders": {"all": "全部", "mega": "MEGA", "azure": "Azure", "aws": "AWS", "google": "Google"}}, "digitalHuman": {"title": "数字人管理", "description": "这是TailAdmin的React.js数字人管理页面 - React.js Tailwind CSS管理仪表板模板", "breadcrumb": "数字人管理", "query": "查询", "collapse": "收起", "queryComplete": "查询完成！", "formReset": "表单已重置", "createNew": "新增数字人", "createSuccess": "数字人创建成功！", "editSuccess": "数字人编辑成功！", "deleteSuccess": "数字人删除成功！", "copied": "已复制", "copyFailed": "复制失败", "sortBy": "排序方式", "createDate": "创建日期", "gender": {"label": "性别", "placeholder": "请选择性别", "female": "女性", "male": "男性"}, "age": {"label": "年龄", "placeholder": "请选择年龄", "adult": "成人", "youngAdult": "青年", "senior": "年长的", "kid": "孩子"}, "order": {"label": "定制", "placeholder": "请选择定制", "newlyAdded": "新增", "hd": "HD", "pro": "专业版专属", "freeSpeech": "自由言论"}, "situation": {"label": "场景", "placeholder": "请选择场景", "aiAvatar": "AI虚拟形象", "airport": "机场", "asmr": "ASMR", "balcony": "阳台", "bathroom": "浴室", "beach": "海滩", "boat": "船", "car": "汽车", "christmas": "圣诞节🎄", "coffeeShop": "咖啡店", "cooking": "烹饪", "drink": "饮品", "family": "家庭", "firefighter": "消防员", "formal": "正式场合", "gaming": "游戏", "greenScreen": "绿幕", "grwm": "GRWM", "gym": "健身房", "hannukah": "光明节", "historical": "历史场景", "home": "家", "hook": "钩子", "interview": "访谈", "kitchen": "厨房", "mall": "商场", "medical": "医疗", "movement": "运动", "multiFrame": "多帧", "nature": "自然", "newsAnchor": "新闻主播", "night": "夜晚", "office": "办公室", "outside": "户外", "plane": "飞机", "podcast": "播客", "pointing": "指向", "pool": "泳池", "pregnant": "孕妇", "reverse": "反转", "sitting": "坐姿", "skit": "短剧", "snow": "雪", "store": "商店", "streaming": "流媒体", "street": "街道", "studio": "演播室", "talk": "谈话", "walking": "行走", "yoga": "瑜伽"}, "accessories": {"label": "配件", "placeholder": "请选择配件", "bags": "包", "bathrobe": "浴袍", "book": "书", "candle": "蜡烛", "cards": "卡片", "dishes": "盘子", "drink": "饮料", "dumbbells": "哑铃", "food": "食物", "fridge": "冰箱", "fruit": "水果", "glasses": "眼镜", "guitar": "吉他", "hat": "帽子", "headphone": "耳机", "hijab": "头巾", "jar": "罐子", "jewels": "珠宝", "knit": "针织品", "laptop": "笔记本电脑", "mic": "麦克风", "mirror": "镜子", "mug": "马克杯", "pet": "宠物", "phone": "电话", "piano": "钢琴", "plant": "植物", "present": "礼物", "scarf": "围巾", "shoes": "鞋子", "suit": "套装", "tools": "工具", "trashCan": "垃圾桶", "tree": "树"}, "emotion": {"label": "情绪", "placeholder": "请选择情绪", "calm": "平静", "enthusiastic": "热情", "excited": "兴奋", "frustrated": "沮丧", "sad": "悲伤", "serious": "严肃", "smiling": "微笑"}, "skinColor": {"label": "肤色", "placeholder": "请选择肤色"}, "category": {"label": "数字人类目", "placeholder": "请选择数字人类目", "virtualHost": "虚拟主播", "teachingAssistant": "教学助手", "entertainmentHost": "娱乐主持", "businessRepresentative": "商务代表", "medicalAssistant": "医疗助手"}, "status": {"label": "状态", "placeholder": "请选择状态", "enabled": "启用", "disabled": "禁用"}, "sort": {"placeholder": "请选择排序方式", "popular": "最受欢迎", "newest": "最新上线", "aToZ": "A-Z", "zToA": "Z-A"}, "table": {"name": "名称", "cover": "封面", "category": "分类", "memberType": "会员类型", "usageCount": "使用次数", "favoriteCount": "收藏次数", "createDate": "创建日期", "actions": "操作"}, "actions": {"edit": "编辑", "copy": "复制ID", "delete": "删除"}}, "renderLog": {"title": "渲染日志", "description": "这是TailAdmin的React.js渲染日志页面 - React.js Tailwind CSS管理仪表板模板", "breadcrumb": "渲染日志", "conditionQuery": "条件查询", "conditionField": {"select": "请选择", "userId": "用户ID", "userName": "名称", "userEmail": "邮箱"}, "conditionValue": {"placeholder": "请输入关键词"}, "generateDate": "生成日期", "source": "来源", "status": "状态", "query": "查询", "collapse": "收起", "reset": "重置", "tabs": {"all": "全部", "digitalAvatar": "口播数字人"}, "fileId": "文档ID", "id": "ID", "userId": "用户ID", "fileContent": "文档内容", "generateTime": "渲染时间", "actions": "操作", "viewDetails": "查看详情", "unavailable": "不可用", "copy": "复制", "copied": "已复制", "copyFailed": "复制失败", "sources": {"all": "全部", "preview": "预览", "export": "导出"}, "statuses": {"all": "全部", "success": "成功", "failed": "失败", "generating": "生成中"}}, "inviteCodeManagement": {"title": "邀请码管理", "description": "这是TailAdmin的React.js邀请码管理页面 - React.js Tailwind CSS管理仪表板模板", "breadcrumb": "邀请码管理", "conditionQuery": "条件查询", "conditionField": {"select": "请选择条件", "id": "ID", "userName": "名称", "userEmail": "邮箱", "inviteCode": "邀请码"}, "conditionValue": {"placeholder": "请输入查询内容"}, "registerTime": "注册时间", "region": "地区", "createInviteCode": "新建邀请码", "query": "查询", "collapse": "收起", "inviteCode": "邀请码", "inviter": "邀请人", "userName": "用户名称", "userEmail": "用户邮箱", "usedInviteCodes": "已使用邀请码", "reward": "获得奖励", "actions": "操作", "details": "详情", "status": {"active": "活跃", "used": "已用完", "expired": "已过期", "unknown": "未知"}, "regions": {"all": "全部地区", "china": "中国", "singapore": "新加坡", "usa": "美国", "uk": "英国", "canada": "加拿大", "hongkong": "香港"}, "messages": {"queryComplete": "查询完成！", "formReset": "表单已重置", "createInviteCodeInDevelopment": "新建邀请码功能开发中", "viewingDetails": "查看邀请码 {code} 详情", "copied": "已复制", "copyFailed": "复制失败"}}}, "common": {"language": "语言", "english": "English", "chinese": "中文", "loading": "加载中...", "save": "保存", "cancel": "取消", "edit": "编辑", "delete": "删除", "view": "查看", "add": "添加", "close": "关闭", "submit": "提交", "reset": "重置", "start": "开始", "end": "结束", "goBack": "返回"}, "pagination": {"total": "共 {{total}} 条", "page": "页", "goTo": "跳至", "itemsPerPage": "条/页"}}
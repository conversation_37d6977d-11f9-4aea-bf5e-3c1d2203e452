import { ReactComponent as PlusIcon } from "./plus.svg?react";
import { ReactComponent as CloseIcon } from "./close.svg?react";
import { ReactComponent as BoxIcon } from "./box.svg?react";
import { ReactComponent as CheckCircleIcon } from "./check-circle.svg?react";
import { ReactComponent as AlertIcon } from "./alert.svg?react";
import { ReactComponent as InfoIcon } from "./info.svg?react";
import { ReactComponent as ErrorIcon } from "./info-error.svg?react";
import { ReactComponent as BoltIcon } from "./bolt.svg?react";
import { ReactComponent as ArrowUpIcon } from "./arrow-up.svg?react";
import { ReactComponent as ArrowDownIcon } from "./arrow-down.svg?react";
import { ReactComponent as FolderIcon } from "./folder.svg?react";
import { ReactComponent as VideoIcon } from "./videos.svg?react";
import { ReactComponent as AudioIcon } from "./audio.svg?react";
import { ReactComponent as GridIcon } from "./grid.svg?react";
import { ReactComponent as FileIcon } from "./file.svg?react";
import { ReactComponent as DownloadIcon } from "./download.svg?react";
import { ReactComponent as ArrowRightIcon } from "./arrow-right.svg?react";
import { ReactComponent as GroupIcon } from "./group.svg?react";
import { ReactComponent as BoxIconLine } from "./box-line.svg?react";
import { ReactComponent as ShootingStarIcon } from "./shooting-star.svg?react";
import { ReactComponent as DollarLineIcon } from "./dollar-line.svg?react";
import { ReactComponent as TrashBinIcon } from "./trash.svg?react";
import { ReactComponent as AngleUpIcon } from "./angle-up.svg?react";
import { ReactComponent as AngleDownIcon } from "./angle-down.svg?react";
import { ReactComponent as AngleLeftIcon } from "./angle-left.svg?react";
import { ReactComponent as AngleRightIcon } from "./angle-right.svg?react";
import { ReactComponent as PencilIcon } from "./pencil.svg?react";
import { ReactComponent as CheckLineIcon } from "./check-line.svg?react";
import { ReactComponent as CloseLineIcon } from "./close-line.svg?react";
import { ReactComponent as ChevronDownIcon } from "./chevron-down.svg?react";
import { ReactComponent as ChevronUpIcon } from "./chevron-up.svg?react";
import { ReactComponent as PaperPlaneIcon } from "./paper-plane.svg?react";
import { ReactComponent as LockIcon } from "./lock.svg?react";
import { ReactComponent as EnvelopeIcon } from "./envelope.svg?react";
import { ReactComponent as UserIcon } from "./user-line.svg?react";
import { ReactComponent as CalenderIcon } from "./calender-line.svg?react";
import { ReactComponent as EyeIcon } from "./eye.svg?react";
import { ReactComponent as EyeCloseIcon } from "./eye-close.svg?react";
import { ReactComponent as TimeIcon } from "./time.svg?react";
import { ReactComponent as CopyIcon } from "./copy.svg?react";
import { ReactComponent as ChevronLeftIcon } from "./chevron-left.svg?react";
import { ReactComponent as UserCircleIcon } from "./user-circle.svg?react";
import { ReactComponent as TaskIcon } from "./task-icon.svg?react";
import { ReactComponent as ListIcon } from "./list.svg?react";
import { ReactComponent as TableIcon } from "./table.svg?react";
import { ReactComponent as PageIcon } from "./page.svg?react";
import { ReactComponent as PieChartIcon } from "./pie-chart.svg?react";
import { ReactComponent as BoxCubeIcon } from "./box-cube.svg?react";
import { ReactComponent as PlugInIcon } from "./plug-in.svg?react";
import { ReactComponent as DocsIcon } from "./docs.svg?react";
import { ReactComponent as MailIcon } from "./mail-line.svg?react";
import { ReactComponent as HorizontaLDots } from "./horizontal-dots.svg?react";
import { ReactComponent as ChatIcon } from "./chat.svg?react";
import { ReactComponent as MoreDotIcon } from "./moredot.svg?react";
import { ReactComponent as AlertHexaIcon } from "./alert-hexa.svg?react";
import { ReactComponent as ErrorHexaIcon } from "./info-hexa.svg?react";
import { ReactComponent as CloseSmallIcon } from "./close-small.svg?react";
import { ReactComponent as CheckSelectedIcon } from "./check-selected.svg?react";
import { ReactComponent as MenuIcon } from "./menu.svg?react";
import { ReactComponent as SearchIcon } from "./search.svg?react";

export {
  ErrorHexaIcon,
  AlertHexaIcon,
  MoreDotIcon,
  DownloadIcon,
  FileIcon,
  GridIcon,
  AudioIcon,
  VideoIcon,
  BoltIcon,
  PlusIcon,
  BoxIcon,
  CloseIcon,
  CheckCircleIcon,
  AlertIcon,
  InfoIcon,
  ErrorIcon,
  ArrowUpIcon,
  FolderIcon,
  ArrowDownIcon,
  ArrowRightIcon,
  GroupIcon,
  BoxIconLine,
  ShootingStarIcon,
  DollarLineIcon,
  TrashBinIcon,
  AngleUpIcon,
  AngleDownIcon,
  PencilIcon,
  CheckLineIcon,
  CloseLineIcon,
  ChevronDownIcon,
  PaperPlaneIcon,
  EnvelopeIcon,
  LockIcon,
  UserIcon,
  CalenderIcon,
  EyeIcon,
  EyeCloseIcon,
  TimeIcon,
  CopyIcon,
  ChevronLeftIcon,
  UserCircleIcon,
  TaskIcon,
  ListIcon,
  TableIcon,
  PageIcon,
  PieChartIcon,
  BoxCubeIcon,
  PlugInIcon,
  DocsIcon,
  MailIcon,
  HorizontaLDots,
  ChevronUpIcon,
  ChatIcon,
  AngleLeftIcon,
  AngleRightIcon,
  CloseSmallIcon,
  CheckSelectedIcon,
  MenuIcon,
  SearchIcon,
};

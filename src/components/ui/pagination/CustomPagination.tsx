import { Pagination } from "antd";
import { useTranslation } from "react-i18next";

export interface CustomPaginationProps {
  /** 当前页码 */
  current: number;
  /** 总数据条数 */
  total: number;
  /** 每页显示条数 */
  pageSize: number;
  /** 页码改变的回调 */
  onChange: (page: number, pageSize: number) => void;
  /** 每页显示条数改变的回调 */
  onShowSizeChange?: (current: number, size: number) => void;
  /** 是否显示快速跳转 */
  showQuickJumper?: boolean;
  /** 是否显示每页显示条数选择器 */
  showSizeChanger?: boolean;
  /** 是否显示总数 */
  showTotal?: boolean;
  /** 每页显示条数选项 */
  pageSizeOptions?: string[];
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 尺寸 */
  size?: "small" | "default";
}

const CustomPagination: React.FC<CustomPaginationProps> = ({
  current,
  total,
  pageSize,
  onChange,
  onShowSizeChange,
  showQuickJumper = true,
  showSizeChanger = true,
  showTotal = true,
  pageSizeOptions = ["10", "20", "50", "100"],
  disabled = false,
  className = "",
  size = "default",
}) => {
  const { t } = useTranslation();

  // 处理页码变化
  const handlePageChange = (page: number, newPageSize: number) => {
    onChange(page, newPageSize);
  };

  // 处理每页显示条数变化
  const handleShowSizeChange = (current: number, size: number) => {
    if (onShowSizeChange) {
      onShowSizeChange(current, size);
    } else {
      onChange(1, size); // 默认跳转到第一页
    }
  };

  // 自定义显示总数的函数
  const showTotalText = (total: number) => {
    return t("pagination.total", { total: total });
  };

  return (
    <div className={`${className}`}>
      {/* 未来迭代需要，可能需要自定义一些模块，比如总数，go to page 等，这里先包一层 */}
      <Pagination
        current={current}
        total={total}
        pageSize={pageSize}
        onChange={handlePageChange}
        onShowSizeChange={handleShowSizeChange}
        showSizeChanger={showSizeChanger}
        showQuickJumper={showQuickJumper}
        showTotal={showTotal ? showTotalText : undefined}
        disabled={disabled}
        size={size}
        pageSizeOptions={pageSizeOptions}
        hideOnSinglePage={false}
        showLessItems={window.innerWidth < 768} // 移动端显示更少的页码
      />
    </div>
  );
};

export default CustomPagination;

{"name": "tailadmin-react", "private": true, "version": "2.0.2", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@react-jvectormap/core": "^1.0.4", "@react-jvectormap/world": "^1.1.2", "antd": "^5.26.7", "apexcharts": "^4.1.0", "clsx": "^2.1.1", "flatpickr": "^4.6.13", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.5.2", "react-i18next": "^15.6.0", "react-router": "^7.1.5", "swiper": "^11.2.3", "tailwind-merge": "^3.0.1"}, "devDependencies": {"@eslint/js": "^9.19.0", "@tailwindcss/postcss": "^4.0.8", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.2", "tailwindcss": "^4.0.8", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0", "vite-plugin-svgr": "^4.3.0"}, "overrides": {"react-helmet-async": {"react": "^16.8.0 || ^17 || ^18 || ^19"}, "@react-jvectormap/core": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}, "@react-jvectormap/world": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}}}